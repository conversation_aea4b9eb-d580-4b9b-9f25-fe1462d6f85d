import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>erRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom'
import { 
  ThemeProvider, 
  createTheme, 
  CssBaseline,
  AppBar,
  Toolbar,
  Typography,
  Container,
  Box,
  Card,
  CardContent,
  Button,
  Grid,
  TextField,
  Alert,
  Chip,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  Snackbar
} from '@mui/material'
import {
  Work as WorkIcon,
  Description as ResumeIcon,
  Email as CoverLetterIcon,
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
  Menu as MenuIcon,
  CheckCircle as CheckIcon,
  Info as InfoIcon,
  Work as LinkedInIcon,
  Download as DownloadIcon,
  CloudUpload as UploadIcon,
  Email as EmailIcon
} from '@mui/icons-material'
import axios from 'axios'
import LinkedInAutomation from './LinkedInAutomation'

// Dark theme configuration
const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#00d4ff',
      light: '#4de6ff',
      dark: '#0099cc'
    },
    secondary: {
      main: '#ff6b35',
      light: '#ff9566',
      dark: '#cc4a1a'
    },
    background: {
      default: '#0a0a0a',
      paper: '#1a1a1a'
    },
    text: {
      primary: '#ffffff',
      secondary: '#b0b0b0'
    }
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
      fontSize: '2.5rem'
    },
    h2: {
      fontWeight: 600,
      fontSize: '2rem'
    },
    h3: {
      fontWeight: 600,
      fontSize: '1.5rem'
    }
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          background: 'linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%)',
          border: '1px solid #333',
          borderRadius: '12px',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 8px 32px rgba(0, 212, 255, 0.2)'
          }
        }
      }
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '8px',
          textTransform: 'none',
          fontWeight: 600
        }
      }
    }
  }
})

// Navigation component
function Navigation({ mobileOpen, handleDrawerToggle, drawerOpen, toggleDrawer }) {
  const location = useLocation()
  
  const menuItems = [
    { text: '仪表板', icon: <DashboardIcon />, path: '/' },
    { text: '简历生成', icon: <ResumeIcon />, path: '/resume' },
    { text: '求职信生成', icon: <CoverLetterIcon />, path: '/cover-letter' },
    { text: 'LinkedIn自动化', icon: <LinkedInIcon />, path: '/linkedin' },
    { text: '设置', icon: <SettingsIcon />, path: '/settings' }
  ]

  const drawer = (
    <Box sx={{ width: drawerOpen ? 250 : 70, height: '100%', bgcolor: 'background.paper', transition: 'width 0.3s ease' }}>
      <Box sx={{ p: 2, borderBottom: '1px solid #333', display: 'flex', justifyContent: drawerOpen ? 'space-between' : 'center', alignItems: 'center' }}>
        {drawerOpen ? (
          <>
            <Box>
              <Typography variant="h6" sx={{ color: 'primary.main', fontWeight: 700 }}>
                AIHawk
              </Typography>
              <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                智能求职助手
              </Typography>
            </Box>
            <IconButton onClick={toggleDrawer} sx={{ color: 'text.secondary' }}>
              <MenuIcon />
            </IconButton>
          </>
        ) : (
          <IconButton onClick={toggleDrawer} sx={{ color: 'primary.main' }}>
            <MenuIcon />
          </IconButton>
        )}
      </Box>
      <List>
        {menuItems.map((item) => (
          <ListItem 
            key={item.text}
            component={Link}
            to={item.path}
            sx={{
              color: location.pathname === item.path ? 'primary.main' : 'text.primary',
              bgcolor: location.pathname === item.path ? 'rgba(0, 212, 255, 0.1)' : 'transparent',
              '&:hover': {
                bgcolor: 'rgba(0, 212, 255, 0.05)'
              },
              justifyContent: drawerOpen ? 'flex-start' : 'center',
              px: drawerOpen ? 2 : 1
            }}
          >
            <ListItemIcon sx={{ color: 'inherit', minWidth: drawerOpen ? 40 : 'auto', mr: drawerOpen ? 2 : 0 }}>
              {item.icon}
            </ListItemIcon>
            {drawerOpen && <ListItemText primary={item.text} />}
          </ListItem>
        ))}
      </List>
    </Box>
  )

  return (
    <>
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{ keepMounted: true }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 250 }
        }}
      >
        {drawer}
      </Drawer>
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', md: 'block' },
          '& .MuiDrawer-paper': { 
            boxSizing: 'border-box', 
            width: drawerOpen ? 250 : 70,
            transition: 'width 0.3s ease',
            overflowX: 'hidden'
          }
        }}
        open
      >
        {drawer}
      </Drawer>
    </>
  )
}

// Dashboard component
function Dashboard() {
  const [apiStatus, setApiStatus] = useState('')
  const [loading, setLoading] = useState(false)
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' })

  const checkBackendHealth = async () => {
    setLoading(true)
    try {
      await axios.get('http://localhost:8003/api/health')
      setApiStatus('连接正常')
      setSnackbar({ open: true, message: '后端服务连接成功！', severity: 'success' })
    } catch (error) {
      setApiStatus('连接失败')
      setSnackbar({ open: true, message: `连接失败: ${error.message}`, severity: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const features = [
    {
      title: '智能简历生成',
      description: '基于职位描述自动生成匹配的简历',
      icon: <ResumeIcon sx={{ fontSize: 40, color: 'primary.main' }} />,
      path: '/resume'
    },
    {
      title: '求职信生成',
      description: '为特定职位创建个性化求职信',
      icon: <CoverLetterIcon sx={{ fontSize: 40, color: 'secondary.main' }} />,
      path: '/cover-letter'
    }
  ]

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h1" sx={{ mb: 2, background: 'linear-gradient(45deg, #00d4ff, #ff6b35)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>
          AIHawk 智能求职系统
        </Typography>
        <Typography variant="h6" sx={{ color: 'text.secondary', mb: 4 }}>
          让AI助力您的求职之路，打造完美简历和求职信
        </Typography>
      </Box>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <InfoIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">系统状态</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Button 
                  variant="contained" 
                  onClick={checkBackendHealth}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <CheckIcon />}
                >
                  {loading ? '检测中...' : '检测后端连接'}
                </Button>
                {apiStatus && (
                  <Chip 
                    label={apiStatus}
                    color={apiStatus === '连接正常' ? 'success' : 'error'}
                    variant="outlined"
                  />
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <WorkIcon sx={{ mr: 1, color: 'secondary.main' }} />
                <Typography variant="h6">快速开始</Typography>
              </Box>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                1. 输入职位URL或描述<br/>
                2. 生成个性化简历和求职信
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Typography variant="h4" sx={{ mb: 3, color: 'text.primary' }}>核心功能</Typography>
      <Grid container spacing={3}>
        {features.map((feature, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Card sx={{ height: '100%', cursor: 'pointer' }} component={Link} to={feature.path}>
              <CardContent sx={{ textAlign: 'center', p: 3 }}>
                <Box sx={{ mb: 2 }}>
                  {feature.icon}
                </Box>
                <Typography variant="h6" sx={{ mb: 1 }}>
                  {feature.title}
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {feature.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  )
}

// Resume Generator component
function ResumeGenerator() {
  const [jobUrl, setJobUrl] = useState('')
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState(null)
  const [pdfLoading, setPdfLoading] = useState(false)
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' })
  const [uploadedResume, setUploadedResume] = useState(null)
  const [resumeUploading, setResumeUploading] = useState(false)
  const [optimizedResumeHtml, setOptimizedResumeHtml] = useState(null)

  // 清除页面内容的函数
  const clearPageContent = () => {
    setJobUrl('')
    setResult(null)
    setUploadedResume(null)
    setOptimizedResumeHtml(null)
    // 清除localStorage中的相关数据
    localStorage.removeItem('resumeJobUrl')
    localStorage.removeItem('resumeResult')
    localStorage.removeItem('uploadedResume')
    localStorage.removeItem('optimizedResumeHtml')
    localStorage.removeItem('jobInfo')
    localStorage.removeItem('coverLetterHtml')
  }



  // 从localStorage恢复状态
  useEffect(() => {
    const savedJobUrl = localStorage.getItem('resumeJobUrl')
    const savedResult = localStorage.getItem('resumeResult')
    const savedUploadedResume = localStorage.getItem('uploadedResume')
    const savedOptimizedResumeHtml = localStorage.getItem('optimizedResumeHtml')

    if (savedJobUrl) {
      setJobUrl(savedJobUrl)
    }
    if (savedResult) {
      try {
        setResult(JSON.parse(savedResult))
      } catch (e) {
        console.error('Failed to parse saved result:', e)
      }
    }
    if (savedUploadedResume) {
      try {
        setUploadedResume(JSON.parse(savedUploadedResume))
      } catch (e) {
        console.error('Failed to parse saved uploaded resume:', e)
      }
    }
    if (savedOptimizedResumeHtml) {
      setOptimizedResumeHtml(savedOptimizedResumeHtml)
    }
  }, [])



  const handleResumeUpload = async (event) => {
    const file = event.target.files[0]
    if (!file) return

    if (file.type !== 'application/pdf') {
      setSnackbar({ open: true, message: '请上传PDF格式的简历文件', severity: 'warning' })
      return
    }

    setResumeUploading(true)
    try {
      const formData = new FormData()
      formData.append('resume', file)

      const response = await axios.post('http://localhost:8003/api/resume/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      if (response.data.status === 'success') {
        setUploadedResume(response.data.resume_data)
        // 保存到localStorage
        localStorage.setItem('uploadedResume', JSON.stringify(response.data.resume_data))
        setSnackbar({ open: true, message: '简历上传并解析成功！', severity: 'success' })
      } else {
        setSnackbar({ open: true, message: response.data.message, severity: 'error' })
      }
    } catch (error) {
      setSnackbar({ open: true, message: `简历上传失败: ${error.message}`, severity: 'error' })
    } finally {
      setResumeUploading(false)
    }
  }

  const handleGenerate = async () => {
    if (!jobUrl.trim()) {
      setSnackbar({ open: true, message: '请输入职位URL', severity: 'warning' })
      return
    }

    // 保存jobUrl到localStorage
    localStorage.setItem('resumeJobUrl', jobUrl)

    setLoading(true)
    try {
      const requestData = { job_url: jobUrl }
      if (uploadedResume) {
        requestData.user_resume = uploadedResume
      }

      const response = await axios.post('http://localhost:8003/api/resume/generate', requestData)
      setResult(response.data)

      // 保存result到localStorage
      localStorage.setItem('resumeResult', JSON.stringify(response.data))

      // 如果有优化后的简历内容，保存它
      if (response.data.optimized_resume) {
        setOptimizedResumeHtml(response.data.optimized_resume)
        // 保存到localStorage供求职信生成使用
        localStorage.setItem('optimizedResumeHtml', response.data.optimized_resume)
      }

      // 保存职位信息到localStorage供求职信生成使用
      if (response.data.job_info) {
        localStorage.setItem('jobInfo', JSON.stringify(response.data.job_info))
      }

      if (response.data.status === 'success') {
        const message = uploadedResume && response.data.optimized_resume
          ? '简历优化成功！已生成针对性简历内容。'
          : '职位信息解析成功！'
        setSnackbar({ open: true, message, severity: 'success' })
      } else {
        setSnackbar({ open: true, message: response.data.message, severity: 'error' })
      }
    } catch (error) {
      setSnackbar({ open: true, message: `生成失败: ${error.message}`, severity: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const handleGeneratePDF = async () => {
    if (!result || result.status !== 'success') {
      setSnackbar({ open: true, message: '请先解析职位信息', severity: 'warning' })
      return
    }

    setPdfLoading(true)
    try {
      const requestData = {}

      // 如果有优化后的HTML内容，使用它
      if (optimizedResumeHtml) {
        requestData.optimized_resume_html = optimizedResumeHtml
      }

      const response = await axios.post('http://localhost:8003/api/resume/generate-pdf', requestData)

      if (response.data.status === 'success') {
        // 下载PDF文件
        const pdfData = response.data.pdf_data
        const filename = response.data.filename

        // 创建下载链接
        const byteCharacters = atob(pdfData)
        const byteNumbers = new Array(byteCharacters.length)
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i)
        }
        const byteArray = new Uint8Array(byteNumbers)
        const blob = new Blob([byteArray], { type: 'application/pdf' })

        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        setSnackbar({ open: true, message: 'PDF简历生成并下载成功！', severity: 'success' })
      } else {
        setSnackbar({ open: true, message: response.data.message, severity: 'error' })
      }
    } catch (error) {
      setSnackbar({ open: true, message: `PDF生成失败: ${error.message}`, severity: 'error' })
    } finally {
      setPdfLoading(false)
    }
  }

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h2" sx={{ color: 'primary.main' }}>
          智能简历生成
        </Typography>
        <Button
          variant="outlined"
          color="secondary"
          onClick={clearPageContent}
          sx={{ minWidth: 120 }}
        >
          清除内容
        </Button>
      </Box>
      
      {/* 简历上传区域 */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3 }}>上传现有简历（可选）</Typography>
          <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
            上传您的现有简历，AI将结合职位要求为您生成更有针对性的优化简历
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Button
              variant="outlined"
              component="label"
              startIcon={resumeUploading ? <CircularProgress size={20} /> : <UploadIcon />}
              disabled={resumeUploading}
              sx={{ minWidth: 150 }}
            >
              {resumeUploading ? '上传中...' : '上传PDF简历'}
              <input
                type="file"
                hidden
                accept=".pdf"
                onChange={handleResumeUpload}
              />
            </Button>

            {uploadedResume && (
              <Chip
                label="简历已上传"
                color="success"
                variant="outlined"
                onDelete={() => {
                  setUploadedResume(null)
                  localStorage.removeItem('uploadedResume')
                }}
              />
            )}
          </Box>

          {uploadedResume && (
            <Alert severity="success" sx={{ mb: 2 }}>
              简历解析成功！AI将基于您的简历和目标职位生成优化版本。
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3 }}>输入职位信息</Typography>
          <TextField
            fullWidth
            label="职位URL"
            placeholder="请输入职位链接..."
            value={jobUrl}
            onChange={(e) => setJobUrl(e.target.value)}
            sx={{ mb: 3 }}
            variant="outlined"
          />
          <Button
            variant="contained"
            size="large"
            onClick={handleGenerate}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <ResumeIcon />}
            sx={{ minWidth: 150 }}
          >
            {loading ? (uploadedResume ? '优化中...' : '解析中...') : (uploadedResume ? '生成优化简历' : '解析职位')}
          </Button>
        </CardContent>
      </Card>

      {result && (
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>生成结果</Typography>
            {result.status === 'success' ? (
              <Box>
                <Alert
                  severity={result.fallback_mode ? "warning" : "success"}
                  sx={{ mb: 2 }}
                >
                  {result.message}
                  {result.fallback_mode && (
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="caption" display="block">
                        注意：由于网络连接问题，系统使用了降级模式进行解析，结果可能不够精确。
                      </Typography>
                    </Box>
                  )}
                </Alert>
                {result.job_info && (
                  <Box>
                    <Typography variant="subtitle1" sx={{ mb: 2 }}>📋 解析的职位信息：</Typography>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" sx={{ mb: 1 }}><strong>🏢 公司：</strong>{result.job_info.company}</Typography>
                      <Typography variant="body2" sx={{ mb: 1 }}><strong>💼 职位：</strong>{result.job_info.role}</Typography>
                      <Typography variant="body2" sx={{ mb: 1 }}><strong>📍 地点：</strong>{result.job_info.location}</Typography>
                      {result.job_info.url && (
                        <Typography variant="body2" sx={{ mb: 2 }}>
                          <strong>🔗 链接：</strong>
                          <a href={result.job_info.url} target="_blank" rel="noopener noreferrer" style={{ color: '#1976d2', textDecoration: 'none' }}>
                            {result.job_info.url.length > 50 ? result.job_info.url.substring(0, 50) + '...' : result.job_info.url}
                          </a>
                        </Typography>
                      )}
                    </Box>
                    {result.job_info.description && result.job_info.description !== "职位描述信息不完整" && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="subtitle2" sx={{ mb: 1 }}>📝 职位描述与要求：</Typography>
                        <Box sx={{
                          p: 2,
                          bgcolor: 'white',
                          borderRadius: 1,
                          border: '1px solid #e0e0e0',
                          maxHeight: '300px',
                          overflowY: 'auto'
                        }}>
                          <Typography variant="body2" sx={{
                            whiteSpace: 'pre-wrap',
                            lineHeight: 1.6,
                            color: '#333333'
                          }}>
                            {result.job_info.description}
                          </Typography>
                        </Box>
                      </Box>
                    )}
                    {result.fallback_mode && (
                      <Box sx={{ mt: 2, p: 2, bgcolor: 'warning.light', borderRadius: 1 }}>
                        <Typography variant="caption" sx={{ color: 'warning.dark' }}>
                          💡 提示：为获得更准确的结果，请检查网络连接后重试，或确保Google API服务可正常访问。
                        </Typography>
                      </Box>
                    )}

                    {/* 优化简历预览 */}
                    {optimizedResumeHtml && (
                      <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #333' }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="subtitle1">优化后的简历预览</Typography>
                          <Box>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => {
                                const newWindow = window.open('', '_blank');
                                newWindow.document.write(optimizedResumeHtml);
                                newWindow.document.close();
                              }}
                              sx={{ mr: 1 }}
                            >
                              新窗口预览
                            </Button>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => {
                                const blob = new Blob([optimizedResumeHtml], { type: 'text/html' });
                                const url = URL.createObjectURL(blob);
                                const a = document.createElement('a');
                                a.href = url;
                                a.download = 'resume.html';
                                a.click();
                                URL.revokeObjectURL(url);
                              }}
                            >
                              下载HTML
                            </Button>
                          </Box>
                        </Box>
                        <Box
                          sx={{
                            maxHeight: 500,
                            overflow: 'auto',
                            border: '1px solid #555',
                            borderRadius: 1,
                            bgcolor: 'white',
                            '& iframe': {
                              width: '100%',
                              height: '500px',
                              border: 'none'
                            }
                          }}
                        >
                          <iframe
                            srcDoc={optimizedResumeHtml}
                            style={{ width: '100%', height: '500px', border: 'none' }}
                            title="简历预览"
                          />
                        </Box>
                      </Box>
                    )}

                    {/* PDF生成区域 */}
                    <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #333' }}>
                      <Typography variant="subtitle1" sx={{ mb: 2 }}>生成PDF简历</Typography>



                      {/* PDF生成按钮 */}
                      <Button
                        variant="contained"
                        color="secondary"
                        size="large"
                        onClick={handleGeneratePDF}
                        disabled={pdfLoading}
                        startIcon={pdfLoading ? <CircularProgress size={20} /> : <DownloadIcon />}
                        sx={{ minWidth: 150 }}
                      >
                        {pdfLoading ? '生成中...' : '生成PDF简历'}
                      </Button>
                    </Box>
                  </Box>
                )}
              </Box>
            ) : result.status === 'warning' ? (
              <Box>
                <Alert severity="warning" sx={{ mb: 2 }}>
                  {result.message}
                </Alert>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  系统将尝试使用基础解析模式继续处理您的请求。
                </Typography>
              </Box>
            ) : (
              <Box>
                <Alert severity="error" sx={{ mb: 2 }}>
                  {result.message}
                </Alert>
                <Box sx={{ mt: 2, p: 2, bgcolor: 'error.light', borderRadius: 1 }}>
                  <Typography variant="subtitle2" sx={{ color: 'error.dark', mb: 1 }}>
                    可能的解决方案：
                  </Typography>
                  <Typography variant="caption" sx={{ color: 'error.dark' }} component="div">
                    • 检查网络连接是否正常<br/>
                    • 确认职位URL是否有效<br/>
                    • 稍后重试或联系技术支持<br/>
                    • 检查防火墙或代理设置
                  </Typography>
                </Box>
              </Box>
            )}
          </CardContent>
        </Card>
      )}

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  )
}

// Placeholder components for other routes
function CoverLetterGenerator() {
  const [jobInfo, setJobInfo] = useState(null)
  const [optimizedResumeHtml, setOptimizedResumeHtml] = useState('')
  const [coverLetterHtml, setCoverLetterHtml] = useState('')
  const [loading, setLoading] = useState(false)
  const [pdfLoading, setPdfLoading] = useState(false)
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' })

  // 清除页面内容的函数
  const clearPageContent = () => {
    setJobInfo(null)
    setOptimizedResumeHtml('')
    setCoverLetterHtml('')
    // 清除localStorage中的相关数据
    localStorage.removeItem('jobInfo')
    localStorage.removeItem('optimizedResumeHtml')
    localStorage.removeItem('coverLetterHtml')
    localStorage.removeItem('resumeJobUrl')
    localStorage.removeItem('resumeResult')
    localStorage.removeItem('uploadedResume')
  }



  // 从localStorage获取之前的数据
  useEffect(() => {
    const savedJobInfo = localStorage.getItem('jobInfo')
    const savedOptimizedResume = localStorage.getItem('optimizedResumeHtml')
    const savedCoverLetter = localStorage.getItem('coverLetterHtml')

    if (savedJobInfo) {
      try {
        setJobInfo(JSON.parse(savedJobInfo))
      } catch (e) {
        console.error('Failed to parse saved job info:', e)
      }
    }
    if (savedOptimizedResume) {
      setOptimizedResumeHtml(savedOptimizedResume)
    }
    if (savedCoverLetter) {
      setCoverLetterHtml(savedCoverLetter)
    }
  }, [])

  const handleGenerateCoverLetter = async () => {
    if (!jobInfo || !optimizedResumeHtml) {
      setSnackbar({
        open: true,
        message: '请先在简历生成页面完成简历优化',
        severity: 'warning'
      })
      return
    }

    setLoading(true)
    try {
      const response = await axios.post('http://localhost:8003/api/cover-letter/generate', {
        job_info: jobInfo,
        optimized_resume_html: optimizedResumeHtml
      })

      if (response.data.status === 'success') {
        setCoverLetterHtml(response.data.cover_letter_html)
        localStorage.setItem('coverLetterHtml', response.data.cover_letter_html)
        setSnackbar({
          open: true,
          message: '求职信生成成功！',
          severity: 'success'
        })
      } else {
        throw new Error(response.data.message || '求职信生成失败')
      }
    } catch (error) {
      console.error('求职信生成错误:', error)
      setSnackbar({
        open: true,
        message: error.response?.data?.message || '求职信生成失败，请重试',
        severity: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleGeneratePDF = async () => {
    if (!coverLetterHtml) {
      setSnackbar({
        open: true,
        message: '请先生成求职信',
        severity: 'warning'
      })
      return
    }

    setPdfLoading(true)
    try {
      const response = await axios.post('http://localhost:8003/api/cover-letter/generate-pdf', {
        cover_letter_html: coverLetterHtml
      })

      if (response.data.status === 'success') {
        // 下载PDF
        const link = document.createElement('a')
        link.href = `data:application/pdf;base64,${response.data.pdf_base64}`
        link.download = `求职信-${jobInfo?.company || '目标公司'}-${jobInfo?.role || '目标职位'}.pdf`
        link.click()

        setSnackbar({
          open: true,
          message: 'PDF生成成功！',
          severity: 'success'
        })
      } else {
        throw new Error(response.data.message || 'PDF生成失败')
      }
    } catch (error) {
      console.error('PDF生成错误:', error)
      setSnackbar({
        open: true,
        message: error.response?.data?.message || 'PDF生成失败，请重试',
        severity: 'error'
      })
    } finally {
      setPdfLoading(false)
    }
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h2" sx={{ color: 'primary.main' }}>
          求职信生成
        </Typography>
        <Button
          variant="outlined"
          color="secondary"
          onClick={clearPageContent}
          sx={{ minWidth: 120 }}
        >
          清除内容
        </Button>
      </Box>

      {/* 职位信息显示 */}
      {jobInfo ? (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
              <WorkIcon sx={{ mr: 1, color: 'secondary.main' }} />
              目标职位信息
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={3}>
                <Typography variant="body2" color="text.secondary">公司</Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>{jobInfo.company}</Typography>
              </Grid>
              <Grid item xs={12} md={3}>
                <Typography variant="body2" color="text.secondary">职位</Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>{jobInfo.role}</Typography>
              </Grid>
              <Grid item xs={12} md={3}>
                <Typography variant="body2" color="text.secondary">地点</Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>{jobInfo.location}</Typography>
              </Grid>
              <Grid item xs={12} md={3}>
                <Typography variant="body2" color="text.secondary">职位链接</Typography>
                <Link
                  href={jobInfo.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{
                    color: '#1976d2',
                    textDecoration: 'none',
                    '&:hover': { textDecoration: 'underline' }
                  }}
                >
                  查看原职位 ↗
                </Link>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      ) : (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Alert severity="info">
              请先在"简历生成"页面完成职位解析和简历优化，然后返回此页面生成求职信。
            </Alert>
          </CardContent>
        </Card>
      )}

      {/* 生成求职信按钮 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Button
            variant="contained"
            color="secondary"
            size="large"
            onClick={handleGenerateCoverLetter}
            disabled={loading || !jobInfo || !optimizedResumeHtml}
            startIcon={loading ? <CircularProgress size={20} /> : <EmailIcon />}
            sx={{ minWidth: 200 }}
          >
            {loading ? '生成中...' : '生成该职位求职信'}
          </Button>
        </CardContent>
      </Card>

      {/* 求职信预览 */}
      {coverLetterHtml && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">求职信预览</Typography>
              <Box>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => {
                    const newWindow = window.open('', '_blank')
                    newWindow.document.write(coverLetterHtml)
                    newWindow.document.close()
                  }}
                  sx={{ mr: 1 }}
                >
                  新窗口预览
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => {
                    const blob = new Blob([coverLetterHtml], { type: 'text/html' })
                    const url = URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.href = url
                    a.download = 'cover-letter.html'
                    a.click()
                    URL.revokeObjectURL(url)
                  }}
                >
                  下载HTML
                </Button>
              </Box>
            </Box>
            <Box
              sx={{
                maxHeight: 600,
                overflow: 'auto',
                border: '1px solid #555',
                borderRadius: 1,
                bgcolor: 'white',
                '& iframe': {
                  width: '100%',
                  height: '600px',
                  border: 'none'
                }
              }}
            >
              <iframe
                srcDoc={coverLetterHtml}
                style={{ width: '100%', height: '600px', border: 'none' }}
                title="求职信预览"
              />
            </Box>
          </CardContent>
        </Card>
      )}

      {/* PDF生成区域 */}
      {coverLetterHtml && (
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>生成PDF求职信</Typography>
            <Button
              variant="contained"
              color="secondary"
              size="large"
              onClick={handleGeneratePDF}
              disabled={pdfLoading}
              startIcon={pdfLoading ? <CircularProgress size={20} /> : <DownloadIcon />}
              sx={{ minWidth: 150 }}
            >
              {pdfLoading ? '生成中...' : '生成PDF求职信'}
            </Button>
          </CardContent>
        </Card>
      )}

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  )
}



function Settings() {
  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h2" sx={{ mb: 4 }}>
        设置
      </Typography>
      <Card>
        <CardContent>
          <Typography variant="body1">
            设置功能正在开发中...
          </Typography>
        </CardContent>
      </Card>
    </Container>
  )
}

function App() {
  const [mobileOpen, setMobileOpen] = useState(false)
  const [drawerOpen, setDrawerOpen] = useState(true)

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen)
  }

  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <Router>
        <Box sx={{ display: 'flex' }}>
          <AppBar
            position="fixed"
            sx={{
              width: { md: `calc(100% - ${drawerOpen ? 250 : 70}px)` },
              ml: { md: drawerOpen ? '250px' : '70px' },
              bgcolor: 'background.paper',
              borderBottom: '1px solid #333',
              transition: 'margin-left 0.3s ease, width 0.3s ease'
            }}
            elevation={0}
          >
            <Toolbar>
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{ mr: 2, display: { md: 'none' } }}
              >
                <MenuIcon />
              </IconButton>
              <Typography variant="h6" noWrap component="div" sx={{ color: 'text.primary' }}>
                AIHawk 智能求职系统
              </Typography>
            </Toolbar>
          </AppBar>
          
          <Navigation 
            mobileOpen={mobileOpen} 
            handleDrawerToggle={handleDrawerToggle} 
            drawerOpen={drawerOpen}
            toggleDrawer={toggleDrawer}
          />
          
          <Box
            component="main"
            sx={{
              flexGrow: 1,
              width: { md: `calc(100% - ${drawerOpen ? 250 : 70}px)` },
              minHeight: '100vh',
              bgcolor: 'background.default',
              transition: 'width 0.3s ease'
            }}
          >
            <Toolbar />
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/resume" element={<ResumeGenerator />} />
              <Route path="/cover-letter" element={<CoverLetterGenerator />} />
              <Route path="/linkedin" element={<LinkedInAutomation />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </Box>
        </Box>
      </Router>
    </ThemeProvider>
  )
}

export default App